package meteordevelopment.meteorclient.utils.world;

import io.netty.handler.codec.socks.SocksInitResponseDecoder$State$ConstantPool;
import io.netty.handler.codec.socks.SocksProtocolVersion$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5InitialResponseDecoder$1$ConstantPool;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javassist.ClassMap$ConstantPool;
import javassist.CtNewMethod$ConstantPool;
import javassist.bytecode.CodeAttribute$RuntimeCopyException$ConstantPool;
import javassist.bytecode.SignatureAttribute$ClassSignature$ConstantPool;
import javassist.bytecode.analysis.ControlFlow$Access$ConstantPool;
import javassist.bytecode.annotation.IntegerMemberValue$ConstantPool;
import javassist.compiler.Javac$ConstantPool;
import javassist.util.proxy.Proxy$ConstantPool;
import javax.annotation.meta.TypeQualifierValidator$ConstantPool;
import meteordevelopment.meteorclient.MeteorClient;
import meteordevelopment.meteorclient.events.meteor.MouseScrollEvent$ConstantPool;
import meteordevelopment.meteorclient.events.packets.PacketEvent$Receive$ConstantPool;
import meteordevelopment.meteorclient.events.render.Render3DEvent$ConstantPool;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.mixininterface.IBakedQuad$ConstantPool;
import meteordevelopment.meteorclient.mixininterface.IClientPlayerInteractionManager$ConstantPool;
import meteordevelopment.meteorclient.renderer.Shaders$ConstantPool;
import meteordevelopment.meteorclient.renderer.Texture$Filter$ConstantPool;
import meteordevelopment.meteorclient.settings.BlockDataSetting$ConstantPool;
import meteordevelopment.meteorclient.systems.accounts.MicrosoftLogin$McResponse$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.HudBox$1$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.elements.PlayerRadarHud$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.elements.PotionTimersHud$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.screens.AddHudElementScreen$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.systems.modules.combat.AnchorAura$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.CrystalAura$SwingMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.TridentExp$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.VelocityPlus$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.NameProtect$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.AntiVoid$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.AutoWalk$Direction$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.Flight$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.NoSlow$WebMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.ElytraFly$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.AutoFish$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.ChestSwap$Chestplate$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.InstantRebreak;
import meteordevelopment.meteorclient.systems.modules.render.Breadcrumbs$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.EntityOwner$ProfileResponse$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.ItemHighlight$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$State$11$ConstantPool;
import meteordevelopment.meteorclient.utils.PreInit;
import meteordevelopment.meteorclient.utils.Utils;
import meteordevelopment.meteorclient.utils.misc.IChangeable$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.MeteorStarscript$1$ConstantPool;
import meteordevelopment.meteorclient.utils.notebot.NotebotUtils$OptionalInstrument$ConstantPool;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.meteorclient.utils.player.Rotations;
import meteordevelopment.meteorclient.utils.render.PlayerHeadTexture$ConstantPool;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.starscript.utils.SFunction$ConstantPool;
import net.minecraft.block.AbstractPressurePlateBlock;
import net.minecraft.block.AirBlock;
import net.minecraft.block.AnvilBlock;
import net.minecraft.block.BedBlock;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.BlockWithEntity;
import net.minecraft.block.Blocks;
import net.minecraft.block.ButtonBlock;
import net.minecraft.block.CartographyTableBlock;
import net.minecraft.block.CraftingTableBlock;
import net.minecraft.block.DoorBlock;
import net.minecraft.block.FenceGateBlock;
import net.minecraft.block.GrindstoneBlock;
import net.minecraft.block.LoomBlock;
import net.minecraft.block.NoteBlock;
import net.minecraft.block.ShapeContext;
import net.minecraft.block.SlabBlock;
import net.minecraft.block.StairsBlock;
import net.minecraft.block.StonecutterBlock;
import net.minecraft.block.TrapdoorBlock;
import net.minecraft.block.enums.BlockHalf;
import net.minecraft.block.enums.SlabType;
import net.minecraft.enchantment.Enchantments;
import net.minecraft.entity.Entity;
import net.minecraft.entity.ExperienceOrbEntity;
import net.minecraft.entity.ItemEntity;
import net.minecraft.entity.attribute.EntityAttributes;
import net.minecraft.entity.decoration.ArmorStandEntity;
import net.minecraft.entity.decoration.EndCrystalEntity;
import net.minecraft.entity.effect.StatusEffectUtil;
import net.minecraft.entity.effect.StatusEffects;
import net.minecraft.entity.projectile.ArrowEntity;
import net.minecraft.entity.projectile.thrown.ExperienceBottleEntity;
import net.minecraft.item.BlockItem;
import net.minecraft.item.ItemStack;
import net.minecraft.network.packet.c2s.play.HandSwingC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerInteractBlockC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket.Full;
import net.minecraft.registry.tag.FluidTags;
import net.minecraft.util.ActionResult;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.hit.HitResult.Type;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.math.BlockPos.Mutable;
import net.minecraft.util.math.Direction.Axis;
import net.minecraft.util.shape.VoxelShapes;
import net.minecraft.world.LightType;
import net.minecraft.world.RaycastContext;
import net.minecraft.world.World;
import net.minecraft.world.RaycastContext.FluidHandling;
import net.minecraft.world.RaycastContext.ShapeType;
import org.reflections.Reflections$ConstantPool;
import org.reflections.scanners.Scanners$6$ConstantPool;

public class BlockUtils {
   public static boolean breaking;
   private static boolean breakingThisTick;
   public static final List<Block> shiftBlocks = Arrays.asList(
      Blocks.field_10443,
      Blocks.field_10034,
      Blocks.field_10380,
      Blocks.field_9980,
      Blocks.field_10486,
      Blocks.field_40285,
      Blocks.field_10246,
      Blocks.field_42740,
      Blocks.field_10535,
      Blocks.field_10333,
      Blocks.field_10312,
      Blocks.field_10228,
      Blocks.field_10200,
      Blocks.field_10608,
      Blocks.field_10485,
      Blocks.field_10199,
      Blocks.field_10407,
      Blocks.field_10063,
      Blocks.field_10203,
      Blocks.field_10600,
      Blocks.field_10275,
      Blocks.field_10051,
      Blocks.field_10140,
      Blocks.field_10532,
      Blocks.field_10268,
      Blocks.field_10605,
      Blocks.field_10373,
      Blocks.field_10055,
      Blocks.field_10068,
      Blocks.field_10371,
      Blocks.field_10603
   );
   private static final ThreadLocal<Mutable> EXPOSED_POS = ThreadLocal.withInitial(Mutable::new);

   private BlockUtils() {
   }

   public static boolean isLikeFullCobeBlock(BlockPos pos) {
      return 1.0 >= MeteorClient.mc.field_1687.method_8320(pos).method_26220(MeteorClient.mc.field_1687, pos).method_1105(Axis.field_11052)
         && MeteorClient.mc.field_1687.method_8320(pos).method_26220(MeteorClient.mc.field_1687, pos).method_1105(Axis.field_11052)
            >= PlayerHeadTexture$ConstantPool.const_qJMSVAqwx3grvVd
         && 1.0 >= MeteorClient.mc.field_1687.method_8320(pos).method_26220(MeteorClient.mc.field_1687, pos).method_1105(Axis.field_11048)
         && MeteorClient.mc.field_1687.method_8320(pos).method_26220(MeteorClient.mc.field_1687, pos).method_1105(Axis.field_11048)
            >= ChestSwap$Chestplate$ConstantPool.const_62PRaPVbNgrBQ7B
         && 1.0 >= MeteorClient.mc.field_1687.method_8320(pos).method_26220(MeteorClient.mc.field_1687, pos).method_1105(Axis.field_11051)
         && MeteorClient.mc.field_1687.method_8320(pos).method_26220(MeteorClient.mc.field_1687, pos).method_1105(Axis.field_11051)
            >= SocksProtocolVersion$ConstantPool.const_UFd2FyBaO9vpVCe;
   }

   @PreInit
   public static void init() {
      MeteorClient.EVENT_BUS.subscribe(BlockUtils.class);
   }

   public static boolean place(BlockPos blockPos, FindItemResult findItemResult, int rotationPriority) {
      return place(blockPos, findItemResult, rotationPriority, true);
   }

   public static boolean place(BlockPos blockPos, FindItemResult findItemResult, boolean rotate, int rotationPriority) {
      return place(blockPos, findItemResult, rotate, rotationPriority, true);
   }

   public static boolean place(BlockPos blockPos, FindItemResult findItemResult, boolean rotate, int rotationPriority, boolean checkEntities) {
      return place(blockPos, findItemResult, rotate, rotationPriority, true, checkEntities);
   }

   public static boolean place(BlockPos blockPos, FindItemResult findItemResult, int rotationPriority, boolean checkEntities) {
      return place(blockPos, findItemResult, true, rotationPriority, true, checkEntities);
   }

   public static boolean place(BlockPos blockPos, FindItemResult findItemResult, boolean rotate, int rotationPriority, boolean swingHand, boolean checkEntities) {
      return place(blockPos, findItemResult, rotate, rotationPriority, swingHand, checkEntities, true);
   }

   public static boolean place(
      BlockPos blockPos, FindItemResult findItemResult, boolean rotate, int rotationPriority, boolean swingHand, boolean checkEntities, boolean swapBack
   ) {
      if (findItemResult.isOffhand()) {
         return place(
            blockPos, Hand.field_5810, MeteorClient.mc.field_1724.method_31548().field_7545, rotate, rotationPriority, swingHand, checkEntities, swapBack
         );
      } else {
         return findItemResult.isHotbar()
            ? place(blockPos, Hand.field_5808, findItemResult.slot(), rotate, rotationPriority, swingHand, checkEntities, swapBack)
            : false;
      }
   }

   public static boolean place(
      BlockPos blockPos, Hand hand, int slot, boolean rotate, int rotationPriority, boolean swingHand, boolean checkEntities, boolean swapBack
   ) {
      if (slot >= 0 && slot <= 8) {
         Block toPlace = Blocks.field_10540;
         ItemStack i = hand == Hand.field_5808
            ? MeteorClient.mc.field_1724.method_31548().method_5438(slot)
            : MeteorClient.mc.field_1724.method_31548().method_5438(45);
         if (i.method_7909() instanceof BlockItem blockItem) {
            toPlace = blockItem.method_7711();
         }

         if (!canPlaceBlock(blockPos, checkEntities, toPlace)) {
            return false;
         } else {
            Vec3d hitPos = Vec3d.method_24953(blockPos);
            Direction side = getPlaceSide(blockPos);
            BlockPos neighbour;
            if (side == null) {
               side = Direction.field_11036;
               neighbour = blockPos;
            } else {
               neighbour = blockPos.method_10093(side);
               hitPos = hitPos.method_1031(
                  side.method_10148() * NameProtect$ConstantPool.const_OQ4ESAANengZnrT,
                  side.method_10164() * Shaders$ConstantPool.const_YGBTWDhIC8r7Tjb,
                  side.method_10165() * IBakedQuad$ConstantPool.const_PwjQTfTBqsf40gG
               );
            }

            BlockHitResult bhr = new BlockHitResult(hitPos, side.method_10153(), neighbour, false);
            if (rotate) {
               Rotations.rotate(Rotations.getYaw(hitPos), Rotations.getPitch(hitPos), rotationPriority, () -> {
                  InvUtils.swap(slot, swapBack);
                  interact(bhr, hand, swingHand);
                  if (swapBack) {
                     InvUtils.swapBack();
                  }
               });
            } else {
               InvUtils.swap(slot, swapBack);
               interact(bhr, hand, swingHand);
               if (swapBack) {
                  InvUtils.swapBack();
               }
            }

            return true;
         }
      } else {
         return false;
      }
   }

   public static boolean place_alien(BlockPos blockPos, FindItemResult findItemResult, int rotationPriority, boolean checkEntities) {
      return place_alien(blockPos, findItemResult, true, rotationPriority, true, checkEntities);
   }

   public static boolean place_alien(
      BlockPos blockPos, FindItemResult findItemResult, boolean rotate, int rotationPriority, boolean swingHand, boolean checkEntities
   ) {
      return place_alien(blockPos, findItemResult, rotate, rotationPriority, swingHand, checkEntities, true);
   }

   public static boolean place_alien(
      BlockPos blockPos, FindItemResult findItemResult, boolean rotate, int rotationPriority, boolean swingHand, boolean checkEntities, boolean swapBack
   ) {
      if (findItemResult.isOffhand()) {
         return place_alien(
            blockPos, Hand.field_5810, MeteorClient.mc.field_1724.method_31548().field_7545, rotate, rotationPriority, swingHand, checkEntities, swapBack
         );
      } else {
         return findItemResult.isHotbar()
            ? place_alien(blockPos, Hand.field_5808, findItemResult.slot(), rotate, rotationPriority, swingHand, checkEntities, swapBack)
            : false;
      }
   }

   public static boolean place_alien(
      BlockPos blockPos, Hand hand, int slot, boolean rotate, int rotationPriority, boolean swingHand, boolean checkEntities, boolean swapBack
   ) {
      if (slot >= 0 && slot <= 8) {
         Block toPlace = Blocks.field_10540;
         ItemStack i = hand == Hand.field_5808
            ? MeteorClient.mc.field_1724.method_31548().method_5438(slot)
            : MeteorClient.mc.field_1724.method_31548().method_5438(45);
         if (i.method_7909() instanceof BlockItem blockItem) {
            toPlace = blockItem.method_7711();
         }

         if (!canPlaceBlock(blockPos, checkEntities, toPlace)) {
            return false;
         } else {
            Direction side_alien = getPlaceSide_alien(blockPos);
            if (side_alien == null) {
               return false;
            } else {
               BlockPos pos_alien = blockPos.method_10093(side_alien);
               Direction side_alien2 = side_alien.method_10153();
               Vec3d directionVec_alien = new Vec3d(
                  pos_alien.method_10263()
                     + Socks5InitialResponseDecoder$1$ConstantPool.const_rrzG4j4FLWYB8DE
                     + side_alien2.method_10163().method_10263() * AntiVoid$Mode$ConstantPool.const_6p7K7btj9o9EZAA,
                  pos_alien.method_10264()
                     + Flight$ConstantPool.const_b9QMxiGDGnnNA9O
                     + side_alien2.method_10163().method_10264() * Scanners$6$ConstantPool.const_SUVV3LgyDEwGGQz,
                  pos_alien.method_10260()
                     + TridentExp$ConstantPool.const_LEwMieNKI1N5Da2
                     + side_alien2.method_10163().method_10260() * Texture$Filter$ConstantPool.const_K7fG3Dm2GIQA1TU
               );
               snapAt_alien(directionVec_alien);
               PlayerUtils.swingHand(hand, SwingSide.All);
               BlockHitResult result = new BlockHitResult(directionVec_alien, side_alien2, pos_alien, false);
               Module.sendSequencedPacket(id -> new PlayerInteractBlockC2SPacket(hand, result, id));
               return true;
            }
         }
      } else {
         return false;
      }
   }

   public static void clickBlock(FindItemResult itemResult, BlockPos pos, Direction side, boolean rotate, Hand hand, SwingSide swingSide, int priority) {
      Vec3d directionVec = new Vec3d(
         pos.method_10263()
            + SocksInitResponseDecoder$State$ConstantPool.const_yIliVIPdNNh65T6
            + side.method_10163().method_10263() * VelocityPlus$ConstantPool.const_QG7JGdvFKZx0SBA,
         pos.method_10264()
            + Javac$ConstantPool.const_WVQSWEwTrd79fyV
            + side.method_10163().method_10264() * NotebotUtils$OptionalInstrument$ConstantPool.const_DIrTTOjGwkhBfgj,
         pos.method_10260() + Reflections$ConstantPool.const_aWj7jjY46hyTa5n + side.method_10163().method_10260() * Proxy$ConstantPool.const_EqeRMY28mQ1wx1P
      );
      BlockHitResult result = new BlockHitResult(directionVec, side, pos, false);
      if (rotate) {
         Rotations.rotate(Rotations.getYaw(pos), Rotations.getPitch(pos), priority, () -> {
            InvUtils.swap(itemResult.slot(), true);
            PlayerUtils.swingHand(hand, swingSide);
            Module.sendSequencedPacket(id -> new PlayerInteractBlockC2SPacket(hand, result, id));
            InvUtils.swapBack();
         });
      } else {
         InvUtils.swap(itemResult.slot(), true);
         PlayerUtils.swingHand(hand, swingSide);
         Module.sendSequencedPacket(id -> new PlayerInteractBlockC2SPacket(hand, result, id));
         InvUtils.swapBack();
      }
   }

   public static void snapAt_alien(Vec3d directionVec) {
      float[] angle = getRotation_alien(directionVec);
      snapAt_alien(angle[0], angle[1]);
   }

   public static void snapAt_alien(float yaw, float pitch) {
      MeteorClient.mc
         .method_1562()
         .method_52787(
            new Full(
               MeteorClient.mc.field_1724.method_23317(),
               MeteorClient.mc.field_1724.method_23318(),
               MeteorClient.mc.field_1724.method_23321(),
               yaw,
               pitch,
               MeteorClient.mc.field_1724.method_24828()
            )
         );
   }

   public static float[] getRotation_alien(Vec3d vec) {
      Vec3d eyesPos = getEyesPos();
      return getRotation_alien(eyesPos, vec);
   }

   public static float[] getRotation_alien(Vec3d eyesPos, Vec3d vec) {
      double diffX = vec.field_1352 - eyesPos.field_1352;
      double diffY = vec.field_1351 - eyesPos.field_1351;
      double diffZ = vec.field_1350 - eyesPos.field_1350;
      double diffXZ = Math.sqrt(diffX * diffX + diffZ * diffZ);
      float yaw = (float)Math.toDegrees(Math.atan2(diffZ, diffX)) - IClientPlayerInteractionManager$ConstantPool.const_UbS9QPq6VoSYVOH;
      float pitch = (float)(-Math.toDegrees(Math.atan2(diffY, diffXZ)));
      return new float[]{MathHelper.method_15393(yaw), MathHelper.method_15393(pitch)};
   }

   public static void interact(BlockHitResult blockHitResult, Hand hand, boolean swing) {
      boolean wasSneaking = MeteorClient.mc.field_1724.field_3913.field_3903;
      MeteorClient.mc.field_1724.field_3913.field_3903 = false;
      ActionResult result = MeteorClient.mc.field_1761.method_2896(MeteorClient.mc.field_1724, hand, blockHitResult);
      if (result.method_23666()) {
         if (swing) {
            MeteorClient.mc.field_1724.method_6104(hand);
         } else {
            MeteorClient.mc.method_1562().method_52787(new HandSwingC2SPacket(hand));
         }
      }

      MeteorClient.mc.field_1724.field_3913.field_3903 = wasSneaking;
   }

   public static boolean canPlaceBlock(BlockPos blockPos, boolean checkEntities, Block block) {
      if (blockPos == null) {
         return false;
      } else if (!World.method_25953(blockPos)) {
         return false;
      } else {
         return !MeteorClient.mc.field_1687.method_8320(blockPos).method_45474()
            ? false
            : !checkEntities || MeteorClient.mc.field_1687.method_8628(block.method_9564(), blockPos, ShapeContext.method_16194());
      }
   }

   public static boolean canPlace(BlockPos blockPos, boolean checkEntities) {
      return canPlaceBlock(blockPos, checkEntities, Blocks.field_10540);
   }

   public static boolean canPlace(BlockPos blockPos) {
      return canPlace(blockPos, true);
   }

   public static boolean canPlace_alien(BlockPos pos, double distance, boolean ignoreCrystal) {
      if (getPlaceSide_alien(pos, distance) == null) {
         return false;
      } else {
         return !canReplace_alien(pos) ? false : !hasEntity_alien(pos, ignoreCrystal);
      }
   }

   public static boolean hasEntity_alien(BlockPos pos, boolean ignoreCrystal) {
      for (Entity entity : getEntities_alien(new Box(pos))) {
         if (entity.method_5805()
            && !(entity instanceof ItemEntity)
            && !(entity instanceof ExperienceOrbEntity)
            && !(entity instanceof ExperienceBottleEntity)
            && !(entity instanceof ArrowEntity)
            && (!ignoreCrystal || !(entity instanceof EndCrystalEntity))) {
            if (entity instanceof ArmorStandEntity) {
            }

            return true;
         }
      }

      return false;
   }

   public static List<Entity> getEntities_alien(Box box) {
      List<Entity> list = new ArrayList<>();

      for (Entity entity : MeteorClient.mc.field_1687.method_18112()) {
         if (entity != null && entity.method_5829().method_994(box)) {
            list.add(entity);
         }
      }

      return list;
   }

   public static Direction getPlaceSide_alien(BlockPos pos, double distance) {
      double dis = meteordevelopment.meteorclient.systems.proxies.Proxy$ConstantPool.const_sMXQgO2rypTF4aq;
      Direction side = null;

      for (Direction i : Direction.values()) {
         if (canClick(pos.method_10093(i)) && !canReplace(pos.method_10093(i)) && canSee_alien(pos.method_10093(i), i.method_10153())) {
            double vecDis = MeteorClient.mc
               .field_1724
               .method_33571()
               .method_1025(
                  pos.method_46558()
                     .method_1031(
                        i.method_10163().method_10263() * AutoWalk$Direction$ConstantPool.const_qdyW1binSyOD5ti,
                        i.method_10163().method_10264() * Render3DEvent$ConstantPool.const_Q4beQtXPUkjtWQy,
                        i.method_10163().method_10260() * ClassMap$ConstantPool.const_vZyzLcwv7SrbOoo
                     )
               );
            if (!(MathHelper.method_15355((float)vecDis) > distance) && (side == null || vecDis < dis)) {
               side = i;
               dis = vecDis;
            }
         }
      }

      return side;
   }

   public static Direction getPlaceSide_alien(BlockPos pos) {
      if (pos == null) {
         return null;
      } else {
         double dis = AutoFish$ConstantPool.const_SWC2BXwAqNzuJYw;
         Direction side = null;

         for (Direction i : Direction.values()) {
            if (canClick(pos.method_10093(i)) && !canReplace(pos.method_10093(i)) && isStrictDirection_alien(pos.method_10093(i), i.method_10153())) {
               double vecDis = MeteorClient.mc
                  .field_1724
                  .method_33571()
                  .method_1025(
                     pos.method_46558()
                        .method_1031(
                           i.method_10163().method_10263() * MicrosoftLogin$McResponse$ConstantPool.const_bmC4wWW23WJhXtu,
                           i.method_10163().method_10264() * IntegerMemberValue$ConstantPool.const_aGJe69QLw4F4xCH,
                           i.method_10163().method_10260() * TypeQualifierValidator$ConstantPool.const_Dtgroj3q1vg1t6M
                        )
                  );
               if (side == null || vecDis < dis) {
                  side = i;
                  dis = vecDis;
               }
            }
         }

         return side;
      }
   }

   public static boolean canSee_alien(BlockPos pos, Direction side) {
      if (side == null) {
         return false;
      } else {
         Vec3d testVec = pos.method_46558()
            .method_1031(
               side.method_10163().method_10263() * CrystalAura$SwingMode$ConstantPool.const_y6xDlUaLqV9WLLX,
               side.method_10163().method_10264() * AddHudElementScreen$ConstantPool.const_b9GIFI6TOMjRO8i,
               side.method_10163().method_10260() * CodeAttribute$RuntimeCopyException$ConstantPool.const_8aI7nNfjxTYJXSy
            );
         HitResult result = MeteorClient.mc
            .field_1687
            .method_17742(new RaycastContext(getEyesPos(), testVec, ShapeType.field_17558, FluidHandling.field_1348, MeteorClient.mc.field_1724));
         return result == null || result.method_17783() == Type.field_1333;
      }
   }

   public static boolean canReplace(BlockPos pos) {
      return pos.method_10264() >= 320 ? false : MeteorClient.mc.field_1687.method_8320(pos).method_45474();
   }

   public static boolean canClick(BlockPos pos) {
      return MeteorClient.mc.field_1687.method_8320(pos).method_51367()
         && (!shiftBlocks.contains(getBlock(pos)) && !(getBlock(pos) instanceof BedBlock) || MeteorClient.mc.field_1724.method_5715());
   }

   public static boolean canReplace_alien(BlockPos pos) {
      return pos.method_10264() >= 320 ? false : MeteorClient.mc.field_1687.method_8320(pos).method_45474();
   }

   public static Direction getPlaceSide(BlockPos blockPos) {
      Vec3d lookVec = blockPos.method_46558().method_1020(MeteorClient.mc.field_1724.method_33571());
      double bestRelevancy = HudBox$1$ConstantPool.const_Mu4c4TS5JAvytge;
      Direction bestSide = null;

      for (Direction side : Direction.values()) {
         BlockPos neighbor = blockPos.method_10093(side);
         BlockState state = MeteorClient.mc.field_1687.method_8320(neighbor);
         if (!state.method_26215() && !isClickable(state.method_26204()) && state.method_26227().method_15769()) {
            double relevancy = side.method_10166().method_10172(lookVec.method_10216(), lookVec.method_10214(), lookVec.method_10215())
               * side.method_10171().method_10181();
            if (relevancy > bestRelevancy) {
               bestRelevancy = relevancy;
               bestSide = side;
            }
         }
      }

      return bestSide;
   }

   public static Direction getClosestPlaceSide(BlockPos blockPos) {
      return getClosestPlaceSide(blockPos, MeteorClient.mc.field_1724.method_33571());
   }

   public static Direction getClosestPlaceSide(BlockPos blockPos, Vec3d pos) {
      Direction closestSide = null;
      double closestDistance = MeteorStarscript$1$ConstantPool.const_LRgzDp1N6a1O706;

      for (Direction side : Direction.values()) {
         BlockPos neighbor = blockPos.method_10093(side);
         BlockState state = MeteorClient.mc.field_1687.method_8320(neighbor);
         if (!state.method_26215() && !isClickable(state.method_26204()) && state.method_26227().method_15769()) {
            double distance = pos.method_1028(neighbor.method_10263(), neighbor.method_10264(), neighbor.method_10260());
            if (distance < closestDistance) {
               closestDistance = distance;
               closestSide = side;
            }
         }
      }

      return closestSide;
   }

   @EventHandler(
      priority = 300
   )
   private static void onTickPre(TickEvent.Pre event) {
      breakingThisTick = false;
   }

   @EventHandler(
      priority = -300
   )
   private static void onTickPost(TickEvent.Post event) {
      if (!breakingThisTick && breaking) {
         breaking = false;
         if (MeteorClient.mc.field_1761 != null) {
            MeteorClient.mc.field_1761.method_2925();
         }
      }
   }

   public static boolean breakBlock(BlockPos blockPos, boolean swing) {
      if (!canBreak(blockPos, MeteorClient.mc.field_1687.method_8320(blockPos))) {
         return false;
      } else {
         BlockPos pos = blockPos instanceof Mutable ? new BlockPos(blockPos) : blockPos;
         InstantRebreak ir = Modules.get().get(InstantRebreak.class);
         if (ir != null && ir.isActive() && ir.blockPos.equals(pos) && ir.shouldMine()) {
            ir.sendPacket();
            return true;
         } else {
            if (MeteorClient.mc.field_1761.method_2923()) {
               MeteorClient.mc.field_1761.method_2902(pos, getDirection(blockPos));
            } else {
               MeteorClient.mc.field_1761.method_2910(pos, getDirection(blockPos));
            }

            if (swing) {
               MeteorClient.mc.field_1724.method_6104(Hand.field_5808);
            } else {
               MeteorClient.mc.method_1562().method_52787(new HandSwingC2SPacket(Hand.field_5808));
            }

            breaking = true;
            breakingThisTick = true;
            return true;
         }
      }
   }

   public static boolean canBreak(BlockPos blockPos, BlockState state) {
      return !MeteorClient.mc.field_1724.method_7337() && state.method_26214(MeteorClient.mc.field_1687, blockPos) < 0.0F
         ? false
         : state.method_26218(MeteorClient.mc.field_1687, blockPos) != VoxelShapes.method_1073();
   }

   public static boolean canBreak(BlockPos blockPos) {
      return canBreak(blockPos, MeteorClient.mc.field_1687.method_8320(blockPos));
   }

   public static boolean canInstaBreak(BlockPos blockPos, float breakSpeed) {
      return MeteorClient.mc.field_1724.method_7337() || calcBlockBreakingDelta2(blockPos, breakSpeed) >= 1.0F;
   }

   public static boolean canInstaBreak(BlockPos blockPos) {
      BlockState state = MeteorClient.mc.field_1687.method_8320(blockPos);
      return canInstaBreak(blockPos, MeteorClient.mc.field_1724.method_7351(state));
   }

   public static float calcBlockBreakingDelta2(BlockPos blockPos, float breakSpeed) {
      BlockState state = MeteorClient.mc.field_1687.method_8320(blockPos);
      float f = state.method_26214(MeteorClient.mc.field_1687, blockPos);
      if (f == PacketEvent$Receive$ConstantPool.const_vU8DTXgKyVoK70R) {
         return 0.0F;
      } else {
         int i = MeteorClient.mc.field_1724.method_7305(state) ? 30 : 100;
         return breakSpeed / f / i;
      }
   }

   public static boolean isClickable(Block block) {
      return block instanceof CraftingTableBlock
         || block instanceof AnvilBlock
         || block instanceof LoomBlock
         || block instanceof CartographyTableBlock
         || block instanceof GrindstoneBlock
         || block instanceof StonecutterBlock
         || block instanceof ButtonBlock
         || block instanceof AbstractPressurePlateBlock
         || block instanceof BlockWithEntity
         || block instanceof BedBlock
         || block instanceof FenceGateBlock
         || block instanceof DoorBlock
         || block instanceof NoteBlock
         || block instanceof TrapdoorBlock;
   }

   public static BlockUtils.MobSpawn isValidMobSpawn(BlockPos blockPos, boolean newMobSpawnLightLevel) {
      return isValidMobSpawn(blockPos, MeteorClient.mc.field_1687.method_8320(blockPos), newMobSpawnLightLevel ? 0 : 7);
   }

   public static BlockUtils.MobSpawn isValidMobSpawn(BlockPos blockPos, BlockState blockState, int spawnLightLimit) {
      if (!(blockState.method_26204() instanceof AirBlock)) {
         return BlockUtils.MobSpawn.Never;
      } else {
         BlockPos down = blockPos.method_10074();
         BlockState downState = MeteorClient.mc.field_1687.method_8320(down);
         if (downState.method_26204() == Blocks.field_9987) {
            return BlockUtils.MobSpawn.Never;
         } else {
            if (!topSurface(downState)) {
               if (downState.method_26220(MeteorClient.mc.field_1687, down) != VoxelShapes.method_1077()) {
                  return BlockUtils.MobSpawn.Never;
               }

               if (downState.method_26167(MeteorClient.mc.field_1687, down)) {
                  return BlockUtils.MobSpawn.Never;
               }
            }

            if (MeteorClient.mc.field_1687.method_8314(LightType.field_9282, blockPos) > spawnLightLimit) {
               return BlockUtils.MobSpawn.Never;
            } else {
               return MeteorClient.mc.field_1687.method_8314(LightType.field_9284, blockPos) > spawnLightLimit
                  ? BlockUtils.MobSpawn.Potential
                  : BlockUtils.MobSpawn.Always;
            }
         }
      }
   }

   public static boolean topSurface(BlockState blockState) {
      return blockState.method_26204() instanceof SlabBlock && blockState.method_11654(SlabBlock.field_11501) == SlabType.field_12679
         ? true
         : blockState.method_26204() instanceof StairsBlock && blockState.method_11654(StairsBlock.field_11572) == BlockHalf.field_12619;
   }

   public static Direction getDirection(BlockPos pos) {
      Vec3d eyesPos = new Vec3d(
         MeteorClient.mc.field_1724.method_23317(),
         MeteorClient.mc.field_1724.method_23318() + MeteorClient.mc.field_1724.method_18381(MeteorClient.mc.field_1724.method_18376()),
         MeteorClient.mc.field_1724.method_23321()
      );
      if (pos.method_10264() > eyesPos.field_1351) {
         return MeteorClient.mc.field_1687.method_8320(pos.method_10069(0, -1, 0)).method_45474()
            ? Direction.field_11033
            : MeteorClient.mc.field_1724.method_5735().method_10153();
      } else {
         return !MeteorClient.mc.field_1687.method_8320(pos.method_10069(0, 1, 0)).method_45474()
            ? MeteorClient.mc.field_1724.method_5735().method_10153()
            : Direction.field_11036;
      }
   }

   public static Direction getClickSide_alien(BlockPos pos) {
      Direction side = null;
      double range = ControlFlow$Access$ConstantPool.const_bjjuwjbXclz3wF6;

      for (Direction i : Direction.values()) {
         if (canSee_alien(pos, i)
            && !(MathHelper.method_15355((float)MeteorClient.mc.field_1724.method_33571().method_1025(pos.method_10093(i).method_46558())) > range)) {
            side = i;
            range = MathHelper.method_15355((float)MeteorClient.mc.field_1724.method_33571().method_1025(pos.method_10093(i).method_46558()));
         }
      }

      if (side != null) {
         return side;
      } else {
         side = Direction.field_11036;

         for (Direction ix : Direction.values()) {
            if (isStrictDirection_alien(pos, ix)
               && MeteorClient.mc.field_1687.method_22347(pos.method_10093(ix))
               && !(MathHelper.method_15355((float)MeteorClient.mc.field_1724.method_33571().method_1025(pos.method_10093(ix).method_46558())) > range)) {
               side = ix;
               range = MathHelper.method_15355((float)MeteorClient.mc.field_1724.method_33571().method_1025(pos.method_10093(ix).method_46558()));
            }
         }

         return side;
      }
   }

   public static void clickBlock_alien(BlockPos pos, Direction side, boolean rotate, Hand hand, SwingSide swingSide) {
      Vec3d directionVec = new Vec3d(
         pos.method_10263()
            + MouseScrollEvent$ConstantPool.const_irtasBib77g1j9k
            + side.method_10163().method_10263() * HighwayBuilder$State$11$ConstantPool.const_rO9e4WaGOIWW9sQ,
         pos.method_10264()
            + BlockDataSetting$ConstantPool.const_8l2e2fdqFMWjdT6
            + side.method_10163().method_10264() * Breadcrumbs$ConstantPool.const_9bNH6D9QnGbDkf9,
         pos.method_10260()
            + ItemHighlight$ConstantPool.const_RLIFtnY6ovYin2z
            + side.method_10163().method_10260() * SFunction$ConstantPool.const_a4YBPUYgT8P736l
      );
      PlayerUtils.swingHand(hand, swingSide);
      BlockHitResult result = new BlockHitResult(directionVec, side, pos, false);
      Module.sendSequencedPacket(id -> new PlayerInteractBlockC2SPacket(hand, result, id));
   }

   public static boolean isStrictDirection_alien(BlockPos pos, Direction side) {
      if (MeteorClient.mc.field_1724.method_31478() - pos.method_10264() >= 0 && side == Direction.field_11033) {
         return false;
      } else if (side == Direction.field_11036 && pos.method_10264() + 1 > MeteorClient.mc.field_1724.method_33571().method_10214()) {
         return false;
      } else if (getBlock(pos.method_10093(side)) != Blocks.field_10540
         && getBlock(pos.method_10093(side)) != Blocks.field_9987
         && getBlock(pos.method_10093(side)) != Blocks.field_23152) {
         Vec3d eyePos = getEyesPos();
         Vec3d blockCenter = pos.method_46558();
         ArrayList<Direction> validAxis = new ArrayList<>();
         validAxis.addAll(checkAxis(eyePos.field_1352 - blockCenter.field_1352, Direction.field_11039, Direction.field_11034, false));
         validAxis.addAll(checkAxis(eyePos.field_1351 - blockCenter.field_1351, Direction.field_11033, Direction.field_11036, true));
         validAxis.addAll(checkAxis(eyePos.field_1350 - blockCenter.field_1350, Direction.field_11043, Direction.field_11035, false));
         return validAxis.contains(side);
      } else {
         return false;
      }
   }

   public static ArrayList<Direction> checkAxis(double diff, Direction negativeSide, Direction positiveSide, boolean bothIfInRange) {
      ArrayList<Direction> valid = new ArrayList<>();
      if (diff < PotionTimersHud$ConstantPool.const_t7NDgLTa4Xm2apM) {
         valid.add(negativeSide);
      }

      if (diff > SignatureAttribute$ClassSignature$ConstantPool.const_78p6pV4ZybL9jm7) {
         valid.add(positiveSide);
      }

      if (bothIfInRange) {
         if (!valid.contains(negativeSide)) {
            valid.add(negativeSide);
         }

         if (!valid.contains(positiveSide)) {
            valid.add(positiveSide);
         }
      }

      return valid;
   }

   public static boolean isExposed(BlockPos blockPos) {
      for (Direction direction : Direction.values()) {
         if (!MeteorClient.mc.field_1687.method_8320(EXPOSED_POS.get().method_25505(blockPos, direction)).method_26225()) {
            return true;
         }
      }

      return false;
   }

   public static double getBreakDelta(int slot, BlockState state) {
      float hardness = state.method_26214(null, null);
      return hardness == EntityOwner$ProfileResponse$ConstantPool.const_sDSEATxSo5YNAl1
         ? 0.0
         : getBlockBreakingSpeed(slot, state)
            / hardness
            / (state.method_29291() && !((ItemStack)MeteorClient.mc.field_1724.method_31548().field_7547.get(slot)).method_7951(state) ? 100 : 30);
   }

   private static double getBlockBreakingSpeed(int slot, BlockState block) {
      double speed = ((ItemStack)MeteorClient.mc.field_1724.method_31548().field_7547.get(slot)).method_7924(block);
      if (speed > 1.0) {
         ItemStack tool = MeteorClient.mc.field_1724.method_31548().method_5438(slot);
         int efficiency = Utils.getEnchantmentLevel(tool, Enchantments.field_9131);
         if (efficiency > 0 && !tool.method_7960()) {
            speed += efficiency * efficiency + 1;
         }
      }

      if (StatusEffectUtil.method_5576(MeteorClient.mc.field_1724)) {
         speed *= 1.0F + (StatusEffectUtil.method_5575(MeteorClient.mc.field_1724) + 1) * NoSlow$WebMode$ConstantPool.const_KDJKGcSSHNWxZ2e;
      }

      if (MeteorClient.mc.field_1724.method_6059(StatusEffects.field_5901)) {
         float k = switch (MeteorClient.mc.field_1724.method_6112(StatusEffects.field_5901).method_5578()) {
            case 0 -> CtNewMethod$ConstantPool.const_g9tdKK4vQbAyH3O;
            case 1 -> ElytraFly$ConstantPool.const_Ag1wBiVE1q0WeyK;
            case 2 -> IChangeable$ConstantPool.const_vVSYFrFAciL02YZ;
            default -> AnchorAura$ConstantPool.const_avoBDQZ304nR1cW;
         };
         speed *= k;
      }

      if (MeteorClient.mc.field_1724.method_5777(FluidTags.field_15517)) {
         speed *= MeteorClient.mc.field_1724.method_45325(EntityAttributes.field_51576);
      }

      if (!MeteorClient.mc.field_1724.method_24828()) {
         speed /= PlayerRadarHud$ConstantPool.const_G1LpLglcWSga3GA;
      }

      return speed;
   }

   public static Mutable mutateAround(Mutable mutable, BlockPos origin, int xOffset, int yOffset, int zOffset) {
      return mutable.method_10103(origin.method_10263() + xOffset, origin.method_10264() + yOffset, origin.method_10260() + zOffset);
   }

   public static ArrayList<BlockPos> getSphere(float range) {
      return getSphere(range, MeteorClient.mc.field_1724.method_33571());
   }

   public static ArrayList<BlockPos> getSphere(float range, Vec3d pos) {
      ArrayList<BlockPos> list = new ArrayList<>();

      for (double x = pos.method_10216() - range; x < pos.method_10216() + range; x++) {
         for (double z = pos.method_10215() - range; z < pos.method_10215() + range; z++) {
            for (double y = pos.method_10214() - range; y < pos.method_10214() + range; y++) {
               BlockPos curPos = new BlockPosX(x, y, z);
               if (!(curPos.method_46558().method_1022(pos) > range) && !list.contains(curPos)) {
                  list.add(curPos);
               }
            }
         }
      }

      return list;
   }

   public static Block getBlock(BlockPos pos) {
      return MeteorClient.mc.field_1687.method_8320(pos).method_26204();
   }

   public static boolean hasCrystal(BlockPos pos) {
      for (Entity entity : getEndCrystals(new Box(pos))) {
         if (entity.method_5805() && entity instanceof EndCrystalEntity) {
            return true;
         }
      }

      return false;
   }

   public static List<EndCrystalEntity> getEndCrystals(Box box) {
      List<EndCrystalEntity> list = new ArrayList<>();

      for (Entity entity : MeteorClient.mc.field_1687.method_18112()) {
         if (entity instanceof EndCrystalEntity crystal && crystal.method_5829().method_994(box)) {
            list.add(crystal);
         }
      }

      return list;
   }

   public static ArrayList<BlockPos> findBlockPos(Block block, float range) {
      Vec3d pos = getEyesPos();
      ArrayList<BlockPos> list = new ArrayList<>();

      for (double x = pos.method_10216() - range; x < pos.method_10216() + range; x++) {
         for (double z = pos.method_10215() - range; z < pos.method_10215() + range; z++) {
            for (double y = pos.method_10214() - range; y < pos.method_10214() + range; y++) {
               BlockPos curPos = new BlockPosX(x, y, z);
               if (!(curPos.method_46558().method_1022(pos) > range)
                  && !list.contains(curPos)
                  && MeteorClient.mc.field_1687.method_8320(curPos).method_26204() == block) {
                  list.add(curPos);
               }
            }
         }
      }

      return list;
   }

   public static Vec3d getEyesPos() {
      return MeteorClient.mc.field_1724.method_33571();
   }

   public static enum MobSpawn {
      Never,
      Potential,
      Always;
   }
}
