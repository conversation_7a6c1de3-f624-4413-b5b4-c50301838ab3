package org.plexpt;

import net.fabricmc.mappingio.MappingReader;
import net.fabricmc.mappingio.MappingVisitor;
import net.fabricmc.mappingio.tree.MemoryMappingTree;
import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.regex.*;

public class MappingIoRemapper {

    // Precompiled patterns for better performance
    private static class CompiledMapping {
        final Pattern pattern;
        final String replacement;

        CompiledMapping(String key, String value) {
            this.pattern = Pattern.compile("\\b" + Pattern.quote(key) + "\\b");
            this.replacement = value;
        }
    }

    private static List<CompiledMapping> classPatterns = new ArrayList<>();
    private static List<CompiledMapping> fieldPatterns = new ArrayList<>();
    private static List<CompiledMapping> methodPatterns = new ArrayList<>();

    public static void main(String[] args) throws IOException {
        String mappingsPath = "mappings.tiny";
        String inputDir = "input";
        String outputDir = "output";

        // Create output directory if it doesn't exist
        Path outputPath = Paths.get(outputDir);
        if (!Files.exists(outputPath)) {
            Files.createDirectories(outputPath);
        }

        // 1. Load mappings via Mapping-IO
        MemoryMappingTree tree = new MemoryMappingTree();
        try (Reader reader = Files.newBufferedReader(Paths.get(mappingsPath))) {
            MappingReader.read(reader, tree);
        }

        String intermediaryNamespace = tree.getSrcNamespace(); // usually 'intermediary'
        String namedNamespace = tree.getDstNamespaces().stream().findFirst().orElse(null);
        // pick the first named ns
        if (namedNamespace == null) throw new RuntimeException("No named namespace found");
        System.out.println("intermediaryNamespace = " + intermediaryNamespace);
        System.out.println("namedNamespace = " + namedNamespace);

        // 2. Build mapping tables
        Map<String, String> classMap = new HashMap<>();
        Map<String, String> fieldMap = new HashMap<>();
        Map<String, String> methodMap = new HashMap<>();

        tree.getClasses().forEach(cls -> {
            String interName = cls.getName(intermediaryNamespace);
            String namedName = cls.getName(namedNamespace);
            classMap.put(getSimpleName(interName), getSimpleName(namedName));

            cls.getFields().forEach(f -> {
                String interField = f.getName(intermediaryNamespace);
                String namedField = f.getName(namedNamespace);
                fieldMap.put(interField, namedField);
            });

            cls.getMethods().forEach(m -> {
                String interMethod = m.getName(intermediaryNamespace);
                String namedMethod = m.getName(namedNamespace);
                methodMap.put(interMethod, namedMethod);
            });
        });

//        classMap.entrySet().stream()
//                        .limit(80)
//                                .forEach(e->{
//                                    System.out.println("map.key=" + e.getKey() + "♥value=" + e.getValue());
//                                });
        System.out.println("✅ Loaded mappings: " + classMap.size() + " classes, " + fieldMap.size() + " fields, " + methodMap.size() + " methods");

        // 3. Process all files in input directory
        Path inputPath = Paths.get(inputDir);
        if (!Files.exists(inputPath)) {
            System.err.println("❌ Input directory does not exist: " + inputDir);
            return;
        }

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(inputPath, "*.java")) {
            int processedCount = 0;
            for (Path file : stream) {
                processFile(file, outputPath, classMap, fieldMap, methodMap);
                processedCount++;
            }
            System.out.println("✅ Batch remapping complete: " + processedCount + " files processed");
        }
    }

    private static void processFile(Path inputFile, Path outputDir,
                                  Map<String, String> classMap,
                                  Map<String, String> fieldMap,
                                  Map<String, String> methodMap) throws IOException {
        System.out.println("Processing: " + inputFile.getFileName());

        // Read input source code
        String code = new String(Files.readAllBytes(inputFile));

        // Replace class names
        for (Map.Entry<String, String> entry : classMap.entrySet()) {
            code = code.replaceAll("\\b" + Pattern.quote(entry.getKey()) + "\\b", entry.getValue());
        }

        // Replace field names
        for (Map.Entry<String, String> entry : fieldMap.entrySet()) {
            code = code.replaceAll("\\b" + Pattern.quote(entry.getKey()) + "\\b", entry.getValue());
        }

        // Replace method names
        for (Map.Entry<String, String> entry : methodMap.entrySet()) {
            code = code.replaceAll("\\b" + Pattern.quote(entry.getKey()) + "\\b", entry.getValue());
        }

        // Write output with same filename
        Path outputFile = outputDir.resolve(inputFile.getFileName());
        Files.write(outputFile, code.getBytes());
        System.out.println("✅ Remapped: " + inputFile.getFileName() + " -> " + outputFile);
    }

    private static String getSimpleName(String name) {
        if (name == null) return null;
        return name.contains("/") ? name.substring(name.lastIndexOf('/') + 1) : name;
    }
}
