package meteordevelopment.meteorclient.utils.world;

import io.netty.handler.codec.socks.SocksInitResponseDecoder$State$ConstantPool;
import io.netty.handler.codec.socks.SocksProtocolVersion$ConstantPool;
import io.netty.handler.codec.socksx.v5.Socks5InitialResponseDecoder$1$ConstantPool;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javassist.ClassMap$ConstantPool;
import javassist.CtNewMethod$ConstantPool;
import javassist.bytecode.CodeAttribute$RuntimeCopyException$ConstantPool;
import javassist.bytecode.SignatureAttribute$ClassSignature$ConstantPool;
import javassist.bytecode.analysis.ControlFlow$Access$ConstantPool;
import javassist.bytecode.annotation.IntegerMemberValue$ConstantPool;
import javassist.compiler.Javac$ConstantPool;
import javassist.util.proxy.Proxy$ConstantPool;
import javax.annotation.meta.TypeQualifierValidator$ConstantPool;
import meteordevelopment.meteorclient.MeteorClient;
import meteordevelopment.meteorclient.events.meteor.MouseScrollEvent$ConstantPool;
import meteordevelopment.meteorclient.events.packets.PacketEvent$Receive$ConstantPool;
import meteordevelopment.meteorclient.events.render.Render3DEvent$ConstantPool;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.mixininterface.IBakedQuad$ConstantPool;
import meteordevelopment.meteorclient.mixininterface.IClientPlayerInteractionManager$ConstantPool;
import meteordevelopment.meteorclient.renderer.Shaders$ConstantPool;
import meteordevelopment.meteorclient.renderer.Texture$Filter$ConstantPool;
import meteordevelopment.meteorclient.settings.BlockDataSetting$ConstantPool;
import meteordevelopment.meteorclient.systems.accounts.MicrosoftLogin$McResponse$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.HudBox$1$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.elements.PlayerRadarHud$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.elements.PotionTimersHud$ConstantPool;
import meteordevelopment.meteorclient.systems.hud.screens.AddHudElementScreen$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.Modules;
import meteordevelopment.meteorclient.systems.modules.combat.AnchorAura$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.CrystalAura$SwingMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.TridentExp$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.ggboy.VelocityPlus$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.misc.NameProtect$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.AntiVoid$Mode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.AutoWalk$Direction$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.Flight$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.NoSlow$WebMode$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.movement.elytrafly.ElytraFly$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.AutoFish$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.ChestSwap$Chestplate$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.player.InstantRebreak;
import meteordevelopment.meteorclient.systems.modules.render.Breadcrumbs$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.EntityOwner$ProfileResponse$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.ItemHighlight$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.world.HighwayBuilder$State$11$ConstantPool;
import meteordevelopment.meteorclient.utils.PreInit;
import meteordevelopment.meteorclient.utils.Utils;
import meteordevelopment.meteorclient.utils.misc.IChangeable$ConstantPool;
import meteordevelopment.meteorclient.utils.misc.MeteorStarscript$1$ConstantPool;
import meteordevelopment.meteorclient.utils.notebot.NotebotUtils$OptionalInstrument$ConstantPool;
import meteordevelopment.meteorclient.utils.player.FindItemResult;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.PlayerUtils;
import meteordevelopment.meteorclient.utils.player.Rotations;
import meteordevelopment.meteorclient.utils.render.PlayerHeadTexture$ConstantPool;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.starscript.utils.SFunction$ConstantPool;
import net.minecraft.block.AbstractPressurePlateBlock;
import net.minecraft.block.AirBlock;
import net.minecraft.block.AnvilBlock;
import net.minecraft.block.BedBlock;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.BlockWithEntity;
import net.minecraft.block.Blocks;
import net.minecraft.block.ButtonBlock;
import net.minecraft.block.CartographyTableBlock;
import net.minecraft.block.CraftingTableBlock;
import net.minecraft.block.DoorBlock;
import net.minecraft.block.FenceGateBlock;
import net.minecraft.block.GrindstoneBlock;
import net.minecraft.block.LoomBlock;
import net.minecraft.block.NoteBlock;
import net.minecraft.block.ShapeContext;
import net.minecraft.block.SlabBlock;
import net.minecraft.block.StairsBlock;
import net.minecraft.block.StonecutterBlock;
import net.minecraft.block.TrapdoorBlock;
import net.minecraft.block.enums.BlockHalf;
import net.minecraft.block.enums.SlabType;
import net.minecraft.enchantment.Enchantments;
import net.minecraft.entity.Entity;
import net.minecraft.entity.ExperienceOrbEntity;
import net.minecraft.entity.ItemEntity;
import net.minecraft.entity.attribute.EntityAttributes;
import net.minecraft.entity.decoration.ArmorStandEntity;
import net.minecraft.entity.decoration.EndCrystalEntity;
import net.minecraft.entity.effect.StatusEffectUtil;
import net.minecraft.entity.effect.StatusEffects;
import net.minecraft.entity.projectile.ArrowEntity;
import net.minecraft.entity.projectile.thrown.ExperienceBottleEntity;
import net.minecraft.item.BlockItem;
import net.minecraft.item.ItemStack;
import net.minecraft.network.packet.c2s.play.HandSwingC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerInteractBlockC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket.Full;
import net.minecraft.registry.tag.FluidTags;
import net.minecraft.util.ActionResult;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.hit.HitResult.Type;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.math.BlockPos.Mutable;
import net.minecraft.util.math.Direction.Axis;
import net.minecraft.util.shape.VoxelShapes;
import net.minecraft.world.LightType;
import net.minecraft.world.RaycastContext;
import net.minecraft.world.World;
import net.minecraft.world.RaycastContext.FluidHandling;
import net.minecraft.world.RaycastContext.ShapeType;
import org.reflections.Reflections$ConstantPool;
import org.reflections.scanners.Scanners$6$ConstantPool;

public class BlockUtils {
   public static boolean breaking;
   private static boolean breakingThisTick;
   public static final List<Block> shiftBlocks = Arrays.asList(
      Blocks.ENDER_CHEST,
      Blocks.CHEST,
      Blocks.TRAPPED_CHEST,
      Blocks.CRAFTING_TABLE,
      Blocks.BIRCH_TRAPDOOR,
      Blocks.BAMBOO_TRAPDOOR,
      Blocks.DARK_OAK_TRAPDOOR,
      Blocks.CHERRY_TRAPDOOR,
      Blocks.ANVIL,
      Blocks.BREWING_STAND,
      Blocks.HOPPER,
      Blocks.DROPPER,
      Blocks.DISPENSER,
      Blocks.ACACIA_TRAPDOOR,
      Blocks.ENCHANTING_TABLE,
      Blocks.WHITE_SHULKER_BOX,
      Blocks.ORANGE_SHULKER_BOX,
      Blocks.MAGENTA_SHULKER_BOX,
      Blocks.LIGHT_BLUE_SHULKER_BOX,
      Blocks.YELLOW_SHULKER_BOX,
      Blocks.LIME_SHULKER_BOX,
      Blocks.PINK_SHULKER_BOX,
      Blocks.GRAY_SHULKER_BOX,
      Blocks.CYAN_SHULKER_BOX,
      Blocks.PURPLE_SHULKER_BOX,
      Blocks.BLUE_SHULKER_BOX,
      Blocks.BROWN_SHULKER_BOX,
      Blocks.GREEN_SHULKER_BOX,
      Blocks.RED_SHULKER_BOX,
      Blocks.BLACK_SHULKER_BOX,
      Blocks.SHULKER_BOX
   );
   private static final ThreadLocal<Mutable> EXPOSED_POS = ThreadLocal.withInitial(Mutable::new);

   private BlockUtils() {
   }

   public static boolean isLikeFullCobeBlock(BlockPos pos) {
      return 1.0 >= MeteorClient.mc.world.getBlockState(pos).getCollisionShape(MeteorClient.mc.world, pos).getMax(Axis.Y)
         && MeteorClient.mc.world.getBlockState(pos).getCollisionShape(MeteorClient.mc.world, pos).getMax(Axis.Y)
            >= PlayerHeadTexture$ConstantPool.const_qJMSVAqwx3grvVd
         && 1.0 >= MeteorClient.mc.world.getBlockState(pos).getCollisionShape(MeteorClient.mc.world, pos).getMax(Axis.X)
         && MeteorClient.mc.world.getBlockState(pos).getCollisionShape(MeteorClient.mc.world, pos).getMax(Axis.X)
            >= ChestSwap$Chestplate$ConstantPool.const_62PRaPVbNgrBQ7B
         && 1.0 >= MeteorClient.mc.world.getBlockState(pos).getCollisionShape(MeteorClient.mc.world, pos).getMax(Axis.Z)
         && MeteorClient.mc.world.getBlockState(pos).getCollisionShape(MeteorClient.mc.world, pos).getMax(Axis.Z)
            >= SocksProtocolVersion$ConstantPool.const_UFd2FyBaO9vpVCe;
   }

   @PreInit
   public static void init() {
      MeteorClient.EVENT_BUS.subscribe(BlockUtils.class);
   }

   public static boolean place(BlockPos blockPos, FindItemResult findItemResult, int rotationPriority) {
      return place(blockPos, findItemResult, rotationPriority, true);
   }

   public static boolean place(BlockPos blockPos, FindItemResult findItemResult, boolean rotate, int rotationPriority) {
      return place(blockPos, findItemResult, rotate, rotationPriority, true);
   }

   public static boolean place(BlockPos blockPos, FindItemResult findItemResult, boolean rotate, int rotationPriority, boolean checkEntities) {
      return place(blockPos, findItemResult, rotate, rotationPriority, true, checkEntities);
   }

   public static boolean place(BlockPos blockPos, FindItemResult findItemResult, int rotationPriority, boolean checkEntities) {
      return place(blockPos, findItemResult, true, rotationPriority, true, checkEntities);
   }

   public static boolean place(BlockPos blockPos, FindItemResult findItemResult, boolean rotate, int rotationPriority, boolean swingHand, boolean checkEntities) {
      return place(blockPos, findItemResult, rotate, rotationPriority, swingHand, checkEntities, true);
   }

   public static boolean place(
      BlockPos blockPos, FindItemResult findItemResult, boolean rotate, int rotationPriority, boolean swingHand, boolean checkEntities, boolean swapBack
   ) {
      if (findItemResult.isOffhand()) {
         return place(
            blockPos, Hand.OFF_HAND, MeteorClient.mc.player.getInventory().selectedSlot, rotate, rotationPriority, swingHand, checkEntities, swapBack
         );
      } else {
         return findItemResult.isHotbar()
            ? place(blockPos, Hand.MAIN_HAND, findItemResult.slot(), rotate, rotationPriority, swingHand, checkEntities, swapBack)
            : false;
      }
   }

   public static boolean place(
      BlockPos blockPos, Hand hand, int slot, boolean rotate, int rotationPriority, boolean swingHand, boolean checkEntities, boolean swapBack
   ) {
      if (slot >= 0 && slot <= 8) {
         Block toPlace = Blocks.OBSIDIAN;
         ItemStack i = hand == Hand.MAIN_HAND
            ? MeteorClient.mc.player.getInventory().getStack(slot)
            : MeteorClient.mc.player.getInventory().getStack(45);
         if (i.getItem() instanceof BlockItem blockItem) {
            toPlace = blockItem.getBlock();
         }

         if (!canPlaceBlock(blockPos, checkEntities, toPlace)) {
            return false;
         } else {
            Vec3d hitPos = Vec3d.ofCenter(blockPos);
            Direction side = getPlaceSide(blockPos);
            BlockPos neighbour;
            if (side == null) {
               side = Direction.UP;
               neighbour = blockPos;
            } else {
               neighbour = blockPos.offset(side);
               hitPos = hitPos.add(
                  side.getOffsetX() * NameProtect$ConstantPool.const_OQ4ESAANengZnrT,
                  side.getOffsetY() * Shaders$ConstantPool.const_YGBTWDhIC8r7Tjb,
                  side.getOffsetZ() * IBakedQuad$ConstantPool.const_PwjQTfTBqsf40gG
               );
            }

            BlockHitResult bhr = new BlockHitResult(hitPos, side.getOpposite(), neighbour, false);
            if (rotate) {
               Rotations.rotate(Rotations.getYaw(hitPos), Rotations.getPitch(hitPos), rotationPriority, () -> {
                  InvUtils.swap(slot, swapBack);
                  interact(bhr, hand, swingHand);
                  if (swapBack) {
                     InvUtils.swapBack();
                  }
               });
            } else {
               InvUtils.swap(slot, swapBack);
               interact(bhr, hand, swingHand);
               if (swapBack) {
                  InvUtils.swapBack();
               }
            }

            return true;
         }
      } else {
         return false;
      }
   }

   public static boolean place_alien(BlockPos blockPos, FindItemResult findItemResult, int rotationPriority, boolean checkEntities) {
      return place_alien(blockPos, findItemResult, true, rotationPriority, true, checkEntities);
   }

   public static boolean place_alien(
      BlockPos blockPos, FindItemResult findItemResult, boolean rotate, int rotationPriority, boolean swingHand, boolean checkEntities
   ) {
      return place_alien(blockPos, findItemResult, rotate, rotationPriority, swingHand, checkEntities, true);
   }

   public static boolean place_alien(
      BlockPos blockPos, FindItemResult findItemResult, boolean rotate, int rotationPriority, boolean swingHand, boolean checkEntities, boolean swapBack
   ) {
      if (findItemResult.isOffhand()) {
         return place_alien(
            blockPos, Hand.OFF_HAND, MeteorClient.mc.player.getInventory().selectedSlot, rotate, rotationPriority, swingHand, checkEntities, swapBack
         );
      } else {
         return findItemResult.isHotbar()
            ? place_alien(blockPos, Hand.MAIN_HAND, findItemResult.slot(), rotate, rotationPriority, swingHand, checkEntities, swapBack)
            : false;
      }
   }

   public static boolean place_alien(
      BlockPos blockPos, Hand hand, int slot, boolean rotate, int rotationPriority, boolean swingHand, boolean checkEntities, boolean swapBack
   ) {
      if (slot >= 0 && slot <= 8) {
         Block toPlace = Blocks.OBSIDIAN;
         ItemStack i = hand == Hand.MAIN_HAND
            ? MeteorClient.mc.player.getInventory().getStack(slot)
            : MeteorClient.mc.player.getInventory().getStack(45);
         if (i.getItem() instanceof BlockItem blockItem) {
            toPlace = blockItem.getBlock();
         }

         if (!canPlaceBlock(blockPos, checkEntities, toPlace)) {
            return false;
         } else {
            Direction side_alien = getPlaceSide_alien(blockPos);
            if (side_alien == null) {
               return false;
            } else {
               BlockPos pos_alien = blockPos.offset(side_alien);
               Direction side_alien2 = side_alien.getOpposite();
               Vec3d directionVec_alien = new Vec3d(
                  pos_alien.getX()
                     + Socks5InitialResponseDecoder$1$ConstantPool.const_rrzG4j4FLWYB8DE
                     + side_alien2.getVector().getX() * AntiVoid$Mode$ConstantPool.const_6p7K7btj9o9EZAA,
                  pos_alien.getY()
                     + Flight$ConstantPool.const_b9QMxiGDGnnNA9O
                     + side_alien2.getVector().getY() * Scanners$6$ConstantPool.const_SUVV3LgyDEwGGQz,
                  pos_alien.getZ()
                     + TridentExp$ConstantPool.const_LEwMieNKI1N5Da2
                     + side_alien2.getVector().getZ() * Texture$Filter$ConstantPool.const_K7fG3Dm2GIQA1TU
               );
               snapAt_alien(directionVec_alien);
               PlayerUtils.swingHand(hand, SwingSide.All);
               BlockHitResult result = new BlockHitResult(directionVec_alien, side_alien2, pos_alien, false);
               Module.sendSequencedPacket(id -> new PlayerInteractBlockC2SPacket(hand, result, id));
               return true;
            }
         }
      } else {
         return false;
      }
   }

   public static void clickBlock(FindItemResult itemResult, BlockPos pos, Direction side, boolean rotate, Hand hand, SwingSide swingSide, int priority) {
      Vec3d directionVec = new Vec3d(
         pos.getX()
            + SocksInitResponseDecoder$State$ConstantPool.const_yIliVIPdNNh65T6
            + side.getVector().getX() * VelocityPlus$ConstantPool.const_QG7JGdvFKZx0SBA,
         pos.getY()
            + Javac$ConstantPool.const_WVQSWEwTrd79fyV
            + side.getVector().getY() * NotebotUtils$OptionalInstrument$ConstantPool.const_DIrTTOjGwkhBfgj,
         pos.getZ() + Reflections$ConstantPool.const_aWj7jjY46hyTa5n + side.getVector().getZ() * Proxy$ConstantPool.const_EqeRMY28mQ1wx1P
      );
      BlockHitResult result = new BlockHitResult(directionVec, side, pos, false);
      if (rotate) {
         Rotations.rotate(Rotations.getYaw(pos), Rotations.getPitch(pos), priority, () -> {
            InvUtils.swap(itemResult.slot(), true);
            PlayerUtils.swingHand(hand, swingSide);
            Module.sendSequencedPacket(id -> new PlayerInteractBlockC2SPacket(hand, result, id));
            InvUtils.swapBack();
         });
      } else {
         InvUtils.swap(itemResult.slot(), true);
         PlayerUtils.swingHand(hand, swingSide);
         Module.sendSequencedPacket(id -> new PlayerInteractBlockC2SPacket(hand, result, id));
         InvUtils.swapBack();
      }
   }

   public static void snapAt_alien(Vec3d directionVec) {
      float[] angle = getRotation_alien(directionVec);
      snapAt_alien(angle[0], angle[1]);
   }

   public static void snapAt_alien(float yaw, float pitch) {
      MeteorClient.mc
         .getNetworkHandler()
         .sendPacket(
            new Full(
               MeteorClient.mc.player.getX(),
               MeteorClient.mc.player.getY(),
               MeteorClient.mc.player.getZ(),
               yaw,
               pitch,
               MeteorClient.mc.player.isOnGround()
            )
         );
   }

   public static float[] getRotation_alien(Vec3d vec) {
      Vec3d eyesPos = getEyesPos();
      return getRotation_alien(eyesPos, vec);
   }

   public static float[] getRotation_alien(Vec3d eyesPos, Vec3d vec) {
      double diffX = vec.x - eyesPos.x;
      double diffY = vec.y - eyesPos.y;
      double diffZ = vec.z - eyesPos.z;
      double diffXZ = Math.sqrt(diffX * diffX + diffZ * diffZ);
      float yaw = (float)Math.toDegrees(Math.atan2(diffZ, diffX)) - IClientPlayerInteractionManager$ConstantPool.const_UbS9QPq6VoSYVOH;
      float pitch = (float)(-Math.toDegrees(Math.atan2(diffY, diffXZ)));
      return new float[]{MathHelper.wrapDegrees(yaw), MathHelper.wrapDegrees(pitch)};
   }

   public static void interact(BlockHitResult blockHitResult, Hand hand, boolean swing) {
      boolean wasSneaking = MeteorClient.mc.player.input.sneaking;
      MeteorClient.mc.player.input.sneaking = false;
      ActionResult result = MeteorClient.mc.interactionManager.interactBlock(MeteorClient.mc.player, hand, blockHitResult);
      if (result.shouldSwingHand()) {
         if (swing) {
            MeteorClient.mc.player.swingHand(hand);
         } else {
            MeteorClient.mc.getNetworkHandler().sendPacket(new HandSwingC2SPacket(hand));
         }
      }

      MeteorClient.mc.player.input.sneaking = wasSneaking;
   }

   public static boolean canPlaceBlock(BlockPos blockPos, boolean checkEntities, Block block) {
      if (blockPos == null) {
         return false;
      } else if (!World.isValid(blockPos)) {
         return false;
      } else {
         return !MeteorClient.mc.world.getBlockState(blockPos).isReplaceable()
            ? false
            : !checkEntities || MeteorClient.mc.world.canPlace(block.getDefaultState(), blockPos, ShapeContext.absent());
      }
   }

   public static boolean canPlace(BlockPos blockPos, boolean checkEntities) {
      return canPlaceBlock(blockPos, checkEntities, Blocks.OBSIDIAN);
   }

   public static boolean canPlace(BlockPos blockPos) {
      return canPlace(blockPos, true);
   }

   public static boolean canPlace_alien(BlockPos pos, double getDistance, boolean ignoreCrystal) {
      if (getPlaceSide_alien(pos, getDistance) == null) {
         return false;
      } else {
         return !canReplace_alien(pos) ? false : !hasEntity_alien(pos, ignoreCrystal);
      }
   }

   public static boolean hasEntity_alien(BlockPos pos, boolean ignoreCrystal) {
      for (Entity entity : getEntities_alien(new Box(pos))) {
         if (entity.isAlive()
            && !(entity instanceof ItemEntity)
            && !(entity instanceof ExperienceOrbEntity)
            && !(entity instanceof ExperienceBottleEntity)
            && !(entity instanceof ArrowEntity)
            && (!ignoreCrystal || !(entity instanceof EndCrystalEntity))) {
            if (entity instanceof ArmorStandEntity) {
            }

            return true;
         }
      }

      return false;
   }

   public static List<Entity> getEntities_alien(Box box) {
      List<Entity> list = new ArrayList<>();

      for (Entity entity : MeteorClient.mc.world.getEntities()) {
         if (entity != null && entity.getBoundingBox().intersects(box)) {
            list.add(entity);
         }
      }

      return list;
   }

   public static Direction getPlaceSide_alien(BlockPos pos, double getDistance) {
      double dis = meteordevelopment.meteorclient.systems.proxies.Proxy$ConstantPool.const_sMXQgO2rypTF4aq;
      Direction side = null;

      for (Direction i : Direction.values()) {
         if (canClick(pos.offset(i)) && !canReplace(pos.offset(i)) && canSee_alien(pos.offset(i), i.getOpposite())) {
            double vecDis = MeteorClient.mc
               .player
               .getEyePos()
               .squaredDistanceTo(
                  pos.toCenterPos()
                     .add(
                        i.getVector().getX() * AutoWalk$Direction$ConstantPool.const_qdyW1binSyOD5ti,
                        i.getVector().getY() * Render3DEvent$ConstantPool.const_Q4beQtXPUkjtWQy,
                        i.getVector().getZ() * ClassMap$ConstantPool.const_vZyzLcwv7SrbOoo
                     )
               );
            if (!(MathHelper.sqrt((float)vecDis) > getDistance) && (side == null || vecDis < dis)) {
               side = i;
               dis = vecDis;
            }
         }
      }

      return side;
   }

   public static Direction getPlaceSide_alien(BlockPos pos) {
      if (pos == null) {
         return null;
      } else {
         double dis = AutoFish$ConstantPool.const_SWC2BXwAqNzuJYw;
         Direction side = null;

         for (Direction i : Direction.values()) {
            if (canClick(pos.offset(i)) && !canReplace(pos.offset(i)) && isStrictDirection_alien(pos.offset(i), i.getOpposite())) {
               double vecDis = MeteorClient.mc
                  .player
                  .getEyePos()
                  .squaredDistanceTo(
                     pos.toCenterPos()
                        .add(
                           i.getVector().getX() * MicrosoftLogin$McResponse$ConstantPool.const_bmC4wWW23WJhXtu,
                           i.getVector().getY() * IntegerMemberValue$ConstantPool.const_aGJe69QLw4F4xCH,
                           i.getVector().getZ() * TypeQualifierValidator$ConstantPool.const_Dtgroj3q1vg1t6M
                        )
                  );
               if (side == null || vecDis < dis) {
                  side = i;
                  dis = vecDis;
               }
            }
         }

         return side;
      }
   }

   public static boolean canSee_alien(BlockPos pos, Direction side) {
      if (side == null) {
         return false;
      } else {
         Vec3d testVec = pos.toCenterPos()
            .add(
               side.getVector().getX() * CrystalAura$SwingMode$ConstantPool.const_y6xDlUaLqV9WLLX,
               side.getVector().getY() * AddHudElementScreen$ConstantPool.const_b9GIFI6TOMjRO8i,
               side.getVector().getZ() * CodeAttribute$RuntimeCopyException$ConstantPool.const_8aI7nNfjxTYJXSy
            );
         HitResult result = MeteorClient.mc
            .world
            .raycast(new RaycastContext(getEyesPos(), testVec, ShapeType.COLLIDER, FluidHandling.NONE, MeteorClient.mc.player));
         return result == null || result.getType() == Type.MISS;
      }
   }

   public static boolean canReplace(BlockPos pos) {
      return pos.getY() >= 320 ? false : MeteorClient.mc.world.getBlockState(pos).isReplaceable();
   }

   public static boolean canClick(BlockPos pos) {
      return MeteorClient.mc.world.getBlockState(pos).isSolid()
         && (!shiftBlocks.contains(getBlock(pos)) && !(getBlock(pos) instanceof BedBlock) || MeteorClient.mc.player.isSneaking());
   }

   public static boolean canReplace_alien(BlockPos pos) {
      return pos.getY() >= 320 ? false : MeteorClient.mc.world.getBlockState(pos).isReplaceable();
   }

   public static Direction getPlaceSide(BlockPos blockPos) {
      Vec3d lookVec = blockPos.toCenterPos().subtract(MeteorClient.mc.player.getEyePos());
      double bestRelevancy = HudBox$1$ConstantPool.const_Mu4c4TS5JAvytge;
      Direction bestSide = null;

      for (Direction side : Direction.values()) {
         BlockPos neighbor = blockPos.offset(side);
         BlockState state = MeteorClient.mc.world.getBlockState(neighbor);
         if (!state.isAir() && !isClickable(state.getBlock()) && state.getFluidState().isEmpty()) {
            double relevancy = side.getAxis().choose(lookVec.getX(), lookVec.getY(), lookVec.getZ())
               * side.getDirection().offset();
            if (relevancy > bestRelevancy) {
               bestRelevancy = relevancy;
               bestSide = side;
            }
         }
      }

      return bestSide;
   }

   public static Direction getClosestPlaceSide(BlockPos blockPos) {
      return getClosestPlaceSide(blockPos, MeteorClient.mc.player.getEyePos());
   }

   public static Direction getClosestPlaceSide(BlockPos blockPos, Vec3d pos) {
      Direction closestSide = null;
      double closestDistance = MeteorStarscript$1$ConstantPool.const_LRgzDp1N6a1O706;

      for (Direction side : Direction.values()) {
         BlockPos neighbor = blockPos.offset(side);
         BlockState state = MeteorClient.mc.world.getBlockState(neighbor);
         if (!state.isAir() && !isClickable(state.getBlock()) && state.getFluidState().isEmpty()) {
            double getDistance = pos.squaredDistanceTo(neighbor.getX(), neighbor.getY(), neighbor.getZ());
            if (getDistance < closestDistance) {
               closestDistance = getDistance;
               closestSide = side;
            }
         }
      }

      return closestSide;
   }

   @EventHandler(
      priority = 300
   )
   private static void onTickPre(TickEvent.Pre event) {
      breakingThisTick = false;
   }

   @EventHandler(
      priority = -300
   )
   private static void onTickPost(TickEvent.Post event) {
      if (!breakingThisTick && breaking) {
         breaking = false;
         if (MeteorClient.mc.interactionManager != null) {
            MeteorClient.mc.interactionManager.cancelBlockBreaking();
         }
      }
   }

   public static boolean breakBlock(BlockPos blockPos, boolean swing) {
      if (!canBreak(blockPos, MeteorClient.mc.world.getBlockState(blockPos))) {
         return false;
      } else {
         BlockPos pos = blockPos instanceof Mutable ? new BlockPos(blockPos) : blockPos;
         InstantRebreak ir = Modules.get().get(InstantRebreak.class);
         if (ir != null && ir.isActive() && ir.blockPos.equals(pos) && ir.shouldMine()) {
            ir.sendPacket();
            return true;
         } else {
            if (MeteorClient.mc.interactionManager.isBreakingBlock()) {
               MeteorClient.mc.interactionManager.updateBlockBreakingProgress(pos, getDirection(blockPos));
            } else {
               MeteorClient.mc.interactionManager.attackBlock(pos, getDirection(blockPos));
            }

            if (swing) {
               MeteorClient.mc.player.swingHand(Hand.MAIN_HAND);
            } else {
               MeteorClient.mc.getNetworkHandler().sendPacket(new HandSwingC2SPacket(Hand.MAIN_HAND));
            }

            breaking = true;
            breakingThisTick = true;
            return true;
         }
      }
   }

   public static boolean canBreak(BlockPos blockPos, BlockState state) {
      return !MeteorClient.mc.player.isCreative() && state.getHardness(MeteorClient.mc.world, blockPos) < 0.0F
         ? false
         : state.getOutlineShape(MeteorClient.mc.world, blockPos) != VoxelShapes.empty();
   }

   public static boolean canBreak(BlockPos blockPos) {
      return canBreak(blockPos, MeteorClient.mc.world.getBlockState(blockPos));
   }

   public static boolean canInstaBreak(BlockPos blockPos, float breakSpeed) {
      return MeteorClient.mc.player.isCreative() || calcBlockBreakingDelta2(blockPos, breakSpeed) >= 1.0F;
   }

   public static boolean canInstaBreak(BlockPos blockPos) {
      BlockState state = MeteorClient.mc.world.getBlockState(blockPos);
      return canInstaBreak(blockPos, MeteorClient.mc.player.getBlockBreakingSpeed(state));
   }

   public static float calcBlockBreakingDelta2(BlockPos blockPos, float breakSpeed) {
      BlockState state = MeteorClient.mc.world.getBlockState(blockPos);
      float f = state.getHardness(MeteorClient.mc.world, blockPos);
      if (f == PacketEvent$Receive$ConstantPool.const_vU8DTXgKyVoK70R) {
         return 0.0F;
      } else {
         int i = MeteorClient.mc.player.canHarvest(state) ? 30 : 100;
         return breakSpeed / f / i;
      }
   }

   public static boolean isClickable(Block block) {
      return block instanceof CraftingTableBlock
         || block instanceof AnvilBlock
         || block instanceof LoomBlock
         || block instanceof CartographyTableBlock
         || block instanceof GrindstoneBlock
         || block instanceof StonecutterBlock
         || block instanceof ButtonBlock
         || block instanceof AbstractPressurePlateBlock
         || block instanceof BlockWithEntity
         || block instanceof BedBlock
         || block instanceof FenceGateBlock
         || block instanceof DoorBlock
         || block instanceof NoteBlock
         || block instanceof TrapdoorBlock;
   }

   public static BlockUtils.MobSpawn isValidMobSpawn(BlockPos blockPos, boolean newMobSpawnLightLevel) {
      return isValidMobSpawn(blockPos, MeteorClient.mc.world.getBlockState(blockPos), newMobSpawnLightLevel ? 0 : 7);
   }

   public static BlockUtils.MobSpawn isValidMobSpawn(BlockPos blockPos, BlockState blockState, int spawnLightLimit) {
      if (!(blockState.getBlock() instanceof AirBlock)) {
         return BlockUtils.MobSpawn.Never;
      } else {
         BlockPos down = blockPos.down();
         BlockState downState = MeteorClient.mc.world.getBlockState(down);
         if (downState.getBlock() == Blocks.BEDROCK) {
            return BlockUtils.MobSpawn.Never;
         } else {
            if (!topSurface(downState)) {
               if (downState.getCollisionShape(MeteorClient.mc.world, down) != VoxelShapes.fullCube()) {
                  return BlockUtils.MobSpawn.Never;
               }

               if (downState.isTransparent(MeteorClient.mc.world, down)) {
                  return BlockUtils.MobSpawn.Never;
               }
            }

            if (MeteorClient.mc.world.getLightLevel(LightType.BLOCK, blockPos) > spawnLightLimit) {
               return BlockUtils.MobSpawn.Never;
            } else {
               return MeteorClient.mc.world.getLightLevel(LightType.SKY, blockPos) > spawnLightLimit
                  ? BlockUtils.MobSpawn.Potential
                  : BlockUtils.MobSpawn.Always;
            }
         }
      }
   }

   public static boolean topSurface(BlockState blockState) {
      return blockState.getBlock() instanceof SlabBlock && blockState.get(SlabBlock.TYPE) == SlabType.TOP
         ? true
         : blockState.getBlock() instanceof StairsBlock && blockState.get(StairsBlock.HALF) == BlockHalf.TOP;
   }

   public static Direction getDirection(BlockPos pos) {
      Vec3d eyesPos = new Vec3d(
         MeteorClient.mc.player.getX(),
         MeteorClient.mc.player.getY() + MeteorClient.mc.player.getEyeHeight(MeteorClient.mc.player.getPose()),
         MeteorClient.mc.player.getZ()
      );
      if (pos.getY() > eyesPos.y) {
         return MeteorClient.mc.world.getBlockState(pos.add(0, -1, 0)).isReplaceable()
            ? Direction.DOWN
            : MeteorClient.mc.player.getHorizontalFacing().getOpposite();
      } else {
         return !MeteorClient.mc.world.getBlockState(pos.add(0, 1, 0)).isReplaceable()
            ? MeteorClient.mc.player.getHorizontalFacing().getOpposite()
            : Direction.UP;
      }
   }

   public static Direction getClickSide_alien(BlockPos pos) {
      Direction side = null;
      double range = ControlFlow$Access$ConstantPool.const_bjjuwjbXclz3wF6;

      for (Direction i : Direction.values()) {
         if (canSee_alien(pos, i)
            && !(MathHelper.sqrt((float)MeteorClient.mc.player.getEyePos().squaredDistanceTo(pos.offset(i).toCenterPos())) > range)) {
            side = i;
            range = MathHelper.sqrt((float)MeteorClient.mc.player.getEyePos().squaredDistanceTo(pos.offset(i).toCenterPos()));
         }
      }

      if (side != null) {
         return side;
      } else {
         side = Direction.UP;

         for (Direction ix : Direction.values()) {
            if (isStrictDirection_alien(pos, ix)
               && MeteorClient.mc.world.isAir(pos.offset(ix))
               && !(MathHelper.sqrt((float)MeteorClient.mc.player.getEyePos().squaredDistanceTo(pos.offset(ix).toCenterPos())) > range)) {
               side = ix;
               range = MathHelper.sqrt((float)MeteorClient.mc.player.getEyePos().squaredDistanceTo(pos.offset(ix).toCenterPos()));
            }
         }

         return side;
      }
   }

   public static void clickBlock_alien(BlockPos pos, Direction side, boolean rotate, Hand hand, SwingSide swingSide) {
      Vec3d directionVec = new Vec3d(
         pos.getX()
            + MouseScrollEvent$ConstantPool.const_irtasBib77g1j9k
            + side.getVector().getX() * HighwayBuilder$State$11$ConstantPool.const_rO9e4WaGOIWW9sQ,
         pos.getY()
            + BlockDataSetting$ConstantPool.const_8l2e2fdqFMWjdT6
            + side.getVector().getY() * Breadcrumbs$ConstantPool.const_9bNH6D9QnGbDkf9,
         pos.getZ()
            + ItemHighlight$ConstantPool.const_RLIFtnY6ovYin2z
            + side.getVector().getZ() * SFunction$ConstantPool.const_a4YBPUYgT8P736l
      );
      PlayerUtils.swingHand(hand, swingSide);
      BlockHitResult result = new BlockHitResult(directionVec, side, pos, false);
      Module.sendSequencedPacket(id -> new PlayerInteractBlockC2SPacket(hand, result, id));
   }

   public static boolean isStrictDirection_alien(BlockPos pos, Direction side) {
      if (MeteorClient.mc.player.getBlockY() - pos.getY() >= 0 && side == Direction.DOWN) {
         return false;
      } else if (side == Direction.UP && pos.getY() + 1 > MeteorClient.mc.player.getEyePos().getY()) {
         return false;
      } else if (getBlock(pos.offset(side)) != Blocks.OBSIDIAN
         && getBlock(pos.offset(side)) != Blocks.BEDROCK
         && getBlock(pos.offset(side)) != Blocks.RESPAWN_ANCHOR) {
         Vec3d eyePos = getEyesPos();
         Vec3d blockCenter = pos.toCenterPos();
         ArrayList<Direction> validAxis = new ArrayList<>();
         validAxis.addAll(checkAxis(eyePos.x - blockCenter.x, Direction.WEST, Direction.EAST, false));
         validAxis.addAll(checkAxis(eyePos.y - blockCenter.y, Direction.DOWN, Direction.UP, true));
         validAxis.addAll(checkAxis(eyePos.z - blockCenter.z, Direction.NORTH, Direction.SOUTH, false));
         return validAxis.contains(side);
      } else {
         return false;
      }
   }

   public static ArrayList<Direction> checkAxis(double diff, Direction negativeSide, Direction positiveSide, boolean bothIfInRange) {
      ArrayList<Direction> valid = new ArrayList<>();
      if (diff < PotionTimersHud$ConstantPool.const_t7NDgLTa4Xm2apM) {
         valid.add(negativeSide);
      }

      if (diff > SignatureAttribute$ClassSignature$ConstantPool.const_78p6pV4ZybL9jm7) {
         valid.add(positiveSide);
      }

      if (bothIfInRange) {
         if (!valid.contains(negativeSide)) {
            valid.add(negativeSide);
         }

         if (!valid.contains(positiveSide)) {
            valid.add(positiveSide);
         }
      }

      return valid;
   }

   public static boolean isExposed(BlockPos blockPos) {
      for (Direction direction : Direction.values()) {
         if (!MeteorClient.mc.world.getBlockState(EXPOSED_POS.get().set(blockPos, direction)).isOpaque()) {
            return true;
         }
      }

      return false;
   }

   public static double getBreakDelta(int slot, BlockState state) {
      float hardness = state.getHardness(null, null);
      return hardness == EntityOwner$ProfileResponse$ConstantPool.const_sDSEATxSo5YNAl1
         ? 0.0
         : getBlockBreakingSpeed(slot, state)
            / hardness
            / (state.isToolRequired() && !((ItemStack)MeteorClient.mc.player.getInventory().main.get(slot)).isSuitableFor(state) ? 100 : 30);
   }

   private static double getBlockBreakingSpeed(int slot, BlockState block) {
      double speed = ((ItemStack)MeteorClient.mc.player.getInventory().main.get(slot)).getMiningSpeedMultiplier(block);
      if (speed > 1.0) {
         ItemStack tool = MeteorClient.mc.player.getInventory().getStack(slot);
         int efficiency = Utils.getEnchantmentLevel(tool, Enchantments.EFFICIENCY);
         if (efficiency > 0 && !tool.isEmpty()) {
            speed += efficiency * efficiency + 1;
         }
      }

      if (StatusEffectUtil.hasHaste(MeteorClient.mc.player)) {
         speed *= 1.0F + (StatusEffectUtil.getHasteAmplifier(MeteorClient.mc.player) + 1) * NoSlow$WebMode$ConstantPool.const_KDJKGcSSHNWxZ2e;
      }

      if (MeteorClient.mc.player.hasStatusEffect(StatusEffects.MINING_FATIGUE)) {
         float k = switch (MeteorClient.mc.player.getStatusEffect(StatusEffects.MINING_FATIGUE).getAmplifier()) {
            case 0 -> CtNewMethod$ConstantPool.const_g9tdKK4vQbAyH3O;
            case 1 -> ElytraFly$ConstantPool.const_Ag1wBiVE1q0WeyK;
            case 2 -> IChangeable$ConstantPool.const_vVSYFrFAciL02YZ;
            default -> AnchorAura$ConstantPool.const_avoBDQZ304nR1cW;
         };
         speed *= k;
      }

      if (MeteorClient.mc.player.isSubmergedIn(FluidTags.WATER)) {
         speed *= MeteorClient.mc.player.getAttributeValue(EntityAttributes.PLAYER_SUBMERGED_MINING_SPEED);
      }

      if (!MeteorClient.mc.player.isOnGround()) {
         speed /= PlayerRadarHud$ConstantPool.const_G1LpLglcWSga3GA;
      }

      return speed;
   }

   public static Mutable mutateAround(Mutable mutable, BlockPos origin, int xOffset, int yOffset, int zOffset) {
      return mutable.set(origin.getX() + xOffset, origin.getY() + yOffset, origin.getZ() + zOffset);
   }

   public static ArrayList<BlockPos> getSphere(float range) {
      return getSphere(range, MeteorClient.mc.player.getEyePos());
   }

   public static ArrayList<BlockPos> getSphere(float range, Vec3d pos) {
      ArrayList<BlockPos> list = new ArrayList<>();

      for (double x = pos.getX() - range; x < pos.getX() + range; x++) {
         for (double z = pos.getZ() - range; z < pos.getZ() + range; z++) {
            for (double y = pos.getY() - range; y < pos.getY() + range; y++) {
               BlockPos curPos = new BlockPosX(x, y, z);
               if (!(curPos.toCenterPos().distanceTo(pos) > range) && !list.contains(curPos)) {
                  list.add(curPos);
               }
            }
         }
      }

      return list;
   }

   public static Block getBlock(BlockPos pos) {
      return MeteorClient.mc.world.getBlockState(pos).getBlock();
   }

   public static boolean hasCrystal(BlockPos pos) {
      for (Entity entity : getEndCrystals(new Box(pos))) {
         if (entity.isAlive() && entity instanceof EndCrystalEntity) {
            return true;
         }
      }

      return false;
   }

   public static List<EndCrystalEntity> getEndCrystals(Box box) {
      List<EndCrystalEntity> list = new ArrayList<>();

      for (Entity entity : MeteorClient.mc.world.getEntities()) {
         if (entity instanceof EndCrystalEntity crystal && crystal.getBoundingBox().intersects(box)) {
            list.add(crystal);
         }
      }

      return list;
   }

   public static ArrayList<BlockPos> findBlockPos(Block block, float range) {
      Vec3d pos = getEyesPos();
      ArrayList<BlockPos> list = new ArrayList<>();

      for (double x = pos.getX() - range; x < pos.getX() + range; x++) {
         for (double z = pos.getZ() - range; z < pos.getZ() + range; z++) {
            for (double y = pos.getY() - range; y < pos.getY() + range; y++) {
               BlockPos curPos = new BlockPosX(x, y, z);
               if (!(curPos.toCenterPos().distanceTo(pos) > range)
                  && !list.contains(curPos)
                  && MeteorClient.mc.world.getBlockState(curPos).getBlock() == block) {
                  list.add(curPos);
               }
            }
         }
      }

      return list;
   }

   public static Vec3d getEyesPos() {
      return MeteorClient.mc.player.getEyePos();
   }

   public static enum MobSpawn {
      Never,
      Potential,
      Always;
   }
}
