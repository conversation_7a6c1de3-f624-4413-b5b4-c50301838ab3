package meteordevelopment.meteorclient.systems.modules.ggboy;

import io.netty.handler.codec.socks.SocksCmdResponse$1$ConstantPool;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javassist.CannotCompileException$ConstantPool;
import javassist.bytecode.analysis.Analyzer$ExceptionInfo$ConstantPool;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.gui.themes.meteor.MeteorGuiTheme$ConstantPool;
import meteordevelopment.meteorclient.settings.DoubleSetting;
import meteordevelopment.meteorclient.settings.EnumSetting;
import meteordevelopment.meteorclient.settings.IntSetting;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.systems.modules.Categories;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.systems.modules.combat.Burrow$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.combat.Offhand$ConstantPool;
import meteordevelopment.meteorclient.systems.modules.render.ESP$ConstantPool;
import meteordevelopment.meteorclient.utils.files.StreamUtils$ConstantPool;
import meteordevelopment.meteorclient.utils.player.InvUtils;
import meteordevelopment.meteorclient.utils.player.TitleScreenCredits$ConstantPool;
import meteordevelopment.meteorclient.utils.world.BlockUtilGrim;
import meteordevelopment.meteorclient.utils.world.BlockUtils;
import meteordevelopment.meteorclient.utils.world.SwingSide;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.starscript.compiler.Parser$ConstantPool;
import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.item.HoeItem;
import net.minecraft.item.Items;
import net.minecraft.util.Hand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;

public class AutoPlow extends Module {
   private final SettingGroup sgGeneral = this.settings.getDefaultGroup();
   private final List<Block> plowBlocks = Arrays.asList(Blocks.DIRT, Blocks.COARSE_DIRT, Blocks.GRASS_BLOCK, Blocks.ROOTED_DIRT);
   private final Setting<Double> range = this.sgGeneral
      .add(
         new DoubleSetting.Builder()
            .name(2duUQYyQkl(9B6FpJFLaq(HVniFz4pjT(PhaseEspPlus$PhaseMode$ConstantPool.const_9ZOnfGRDdMgDeBD))))
            .description(2duUQYyQkl(9B6FpJFLaq(HVniFz4pjT(PalletBuilder$ConstantPool.const_bPBVk5lsV61wVjD))))
            .defaultValue(Phase$ConstantPool.const_GtfikIRBMTDIv0h)
            .range(ESP$ConstantPool.const_sld8wBKONKdx8Dv, DoubleAccountTP$Role$ConstantPool.const_1UeIL4TOxO7ivdV)
            .build()
      );
   private final Setting<Integer> placeNums = this.sgGeneral
      .add(
         new IntSetting.Builder()
            .name(2duUQYyQkl(9B6FpJFLaq(HVniFz4pjT(CannotCompileException$ConstantPool.const_tMJWXiYe6IMhvAt))))
            .description(2duUQYyQkl(9B6FpJFLaq(HVniFz4pjT(SocksCmdResponse$1$ConstantPool.const_q4suNHNFP5YswVi))))
            .defaultValue(2)
            .range(1, 6)
            .build()
      );
   private final Setting<AutoPlow.PlowMode> mode = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(2duUQYyQkl(9B6FpJFLaq(HVniFz4pjT(Parser$ConstantPool.const_SfRBjAqfgJQrKrl)))))
                  .description(2duUQYyQkl(9B6FpJFLaq(HVniFz4pjT(TitleScreenCredits$ConstantPool.const_Z6JbaaBGATXBjhD)))))
               .defaultValue(AutoPlow.PlowMode.PLOW))
            .build()
      );
   private final Setting<AutoPlow.SeedType> seed_type = this.sgGeneral
      .add(
         ((EnumSetting.Builder)((EnumSetting.Builder)((EnumSetting.Builder)new EnumSetting.Builder()
                     .name(2duUQYyQkl(9B6FpJFLaq(HVniFz4pjT(Offhand$ConstantPool.const_h4Ln8gIzvoeiqrk)))))
                  .description(2duUQYyQkl(9B6FpJFLaq(HVniFz4pjT(MeteorGuiTheme$ConstantPool.const_1J5ha1R1I1AiTDM)))))
               .defaultValue(AutoPlow.SeedType.WHEAT))
            .build()
      );
   private List<BlockPos> plowPos = new ArrayList<>();
   private List<BlockPos> plantPos = new ArrayList<>();

   public AutoPlow() {
      super(
         Categories.Ggboy,
         2duUQYyQkl(9B6FpJFLaq(HVniFz4pjT(Burrow$ConstantPool.const_eN8VaLiirV9659M))),
         new StringBuilder(2duUQYyQkl(9B6FpJFLaq(HVniFz4pjT(Analyzer$ExceptionInfo$ConstantPool.const_lYWEvq6nhgofbP4)))),
         2duUQYyQkl(9B6FpJFLaq(HVniFz4pjT(StreamUtils$ConstantPool.const_1ntaj4oGAFAa4Ig)))
      );
   }

   @EventHandler
   public void onTick(TickEvent.Pre event) {
      if (this.mode.get() == AutoPlow.PlowMode.PLOWANDPLANT) {
         this.plow();
         this.plant();
      }

      if (this.mode.get() == AutoPlow.PlowMode.PLOW) {
         this.plow();
      }

      if (this.mode.get() == AutoPlow.PlowMode.PLANT) {
         this.plant();
      }
   }

   public void plow() {
      int hoeSlot = InvUtils.findClassInventorySlotGrim(HoeItem.class);
      if (hoeSlot != -1) {
         this.setPlowPos();
         if (!this.plowPos.isEmpty()) {
            int plowTimes = Math.min(this.placeNums.get(), this.plowPos.size());

            for (int i = 0; i < plowTimes; i++) {
               InvUtils.doSwap(hoeSlot);
               BlockUtilGrim.clickBlock(this.plowPos.get(i), Direction.UP, true, Hand.MAIN_HAND, SwingSide.All);
               InvUtils.doSwap(hoeSlot);
            }
         }
      }
   }

   public void plant() {
      int hoeSlot = this.findSeedSlot();
      if (hoeSlot != -1) {
         this.setPlantPos();
         if (!this.plantPos.isEmpty()) {
            int plowTimes = Math.min(this.placeNums.get(), this.plantPos.size());

            for (int i = 0; i < plowTimes; i++) {
               InvUtils.doSwap(hoeSlot);
               BlockUtilGrim.clickBlock(this.plantPos.get(i), Direction.UP, true, Hand.MAIN_HAND, SwingSide.All);
               InvUtils.doSwap(hoeSlot);
            }
         }
      }
   }

   private int findSeedSlot() {
      return InvUtils.findClassInventorySlotGrim(this.seed_type.get().getAClass());
   }

   public void setPlowPos() {
      this.plowPos.clear();

      for (BlockPos pos : BlockUtils.getSphere(Float.parseFloat(this.range.get().toString()))) {
         if (mc.world.getBlockState(pos.up()).getBlock() == Blocks.AIR
            && this.plowBlocks.contains(mc.world.getBlockState(pos).getBlock())) {
            this.plowPos.add(pos);
         }
      }
   }

   public void setPlantPos() {
      this.plantPos.clear();

      for (BlockPos pos : BlockUtils.getSphere(Float.parseFloat(this.range.get().toString()))) {
         if (mc.world.getBlockState(pos.up()).getBlock() == Blocks.AIR
            && mc.world.getBlockState(pos).getBlock() == Blocks.FARMLAND) {
            this.plantPos.add(pos);
         }
      }
   }

   private static enum PlowMode {
      PLOWANDPLANT,
      PLOW,
      PLANT;
   }

   private static enum SeedType {
      BEETROOT(Items.BEETROOT_SEEDS.getClass()),
      CARROT(Items.CARROT.getClass()),
      POTATO(Items.POTATO.getClass()),
      WHEAT(Items.WHEAT_SEEDS.getClass()),
      PUMPKIN(Items.PUMPKIN_SEEDS.getClass()),
      MELON(Items.MELON_SEEDS.getClass());

      Class aClass;

      public Class getAClass() {
         return this.aClass;
      }

      private SeedType(Class aClass) {
         this.aClass = aClass;
      }
   }
}
